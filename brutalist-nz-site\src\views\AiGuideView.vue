<template>
  <div class="ai-guide-page">
    <div class="site-container">
      <div v-if="loading" class="loading-state">
        <p>Loading guide...</p>
      </div>

      <div v-else-if="error" class="error-state">
        <h1>Guide Not Found</h1>
        <p>{{ error }}</p>
        <router-link to="/ai-help" class="back-link">← Back to AI Help</router-link>
      </div>

      <article v-else class="guide-content">
        <nav class="guide-nav">
          <router-link to="/ai-help" class="back-link">← Back to AI Help</router-link>
        </nav>

        <div class="guide-body" v-html="renderedContent"></div>

        <footer class="guide-footer">
          <div class="downloads-section" v-if="guide?.downloads?.length">
            <h3>Download Resources</h3>
            <div class="download-links">
              <a
                v-for="download in guide.downloads"
                :key="download.url"
                :href="download.url"
                target="_blank"
                class="download-btn"
              >
                {{ download.icon }} {{ download.title }}
              </a>
            </div>
          </div>

          <div class="share-section">
            <p><em>Feel free to share this guide - just credit @calmren.com</em></p>
          </div>
        </footer>
      </article>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { marked } from 'marked'
import { useGuides } from '@/composables/useGuides'

const route = useRoute()
const { getGuideBySlug } = useGuides()

const loading = ref(true)
const error = ref('')
const guide = ref<ReturnType<typeof getGuideBySlug>>()

const slug = computed(() => route.params.slug as string)

const renderedContent = computed(() => {
  if (!guide.value?.content) return ''

  // Configure marked options
  marked.setOptions({
    breaks: true,
    gfm: true,
  })

  return marked(guide.value.content)
})

const loadGuide = () => {
  try {
    loading.value = true
    error.value = ''

    const foundGuide = getGuideBySlug(slug.value)
    if (!foundGuide) {
      throw new Error(`Guide "${slug.value}" not found`)
    }

    guide.value = foundGuide
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load guide'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadGuide()
})
</script>

<style scoped>
.ai-guide-page {
  padding: var(--space-8) 0;
}

.loading-state,
.error-state {
  text-align: center;
  padding: var(--space-12) 0;
}

.back-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
}

.back-link:hover {
  text-decoration: underline;
}

.guide-nav {
  margin-bottom: var(--space-6);
}

.guide-content {
  max-width: 800px;
  margin: 0 auto;
}

.guide-body {
  line-height: 1.7;
  font-size: var(--text-base);
}

/* Markdown content styles */
.guide-body :deep(h1) {
  font-size: var(--text-4xl);
  font-weight: bold;
  margin: var(--space-8) 0 var(--space-4) 0;
  line-height: 1.2;
}

.guide-body :deep(h2) {
  font-size: var(--text-2xl);
  font-weight: bold;
  margin: var(--space-6) 0 var(--space-3) 0;
  border-bottom: 2px solid var(--color-border);
  padding-bottom: var(--space-2);
}

.guide-body :deep(h3) {
  font-size: var(--text-xl);
  font-weight: bold;
  margin: var(--space-4) 0 var(--space-2) 0;
}

.guide-body :deep(p) {
  margin-bottom: var(--space-4);
}

.guide-body :deep(ul),
.guide-body :deep(ol) {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

.guide-body :deep(li) {
  margin-bottom: var(--space-1);
}

.guide-body :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: var(--space-4) 0;
  font-size: var(--text-sm);
}

.guide-body :deep(th),
.guide-body :deep(td) {
  border: 1px solid var(--color-border);
  padding: var(--space-3);
  text-align: left;
  vertical-align: top;
}

.guide-body :deep(th) {
  background: var(--color-background-secondary, #f8f9fa);
  font-weight: bold;
}

.guide-body :deep(code) {
  background: var(--color-background-secondary, #f8f9fa);
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.guide-body :deep(pre) {
  background: var(--color-background-secondary, #f8f9fa);
  padding: var(--space-4);
  border-radius: 4px;
  overflow-x: auto;
  margin: var(--space-4) 0;
}

.guide-body :deep(pre code) {
  background: none;
  padding: 0;
}

.guide-body :deep(blockquote) {
  border-left: 4px solid var(--color-primary);
  padding-left: var(--space-4);
  margin: var(--space-4) 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

.guide-body :deep(a) {
  color: var(--color-primary);
  text-decoration: none;
}

.guide-body :deep(a:hover) {
  text-decoration: underline;
}

.guide-footer {
  margin-top: var(--space-12);
  padding-top: var(--space-6);
  border-top: 2px solid var(--color-border);
}

.downloads-section {
  margin-bottom: var(--space-6);
}

.downloads-section h3 {
  margin-bottom: var(--space-3);
}

.download-links {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.download-btn {
  display: inline-block;
  background: var(--color-primary);
  color: white;
  padding: var(--space-3) var(--space-4);
  text-decoration: none;
  font-weight: bold;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background: var(--color-primary-dark, #0056b3);
}

.share-section {
  text-align: center;
  color: var(--color-text-secondary);
}
</style>
