# Guide to Effective Prompt Crafting

*A comprehensive framework for reliable AI results using the C-S-T and P-I-T methods*

## Quick Navigation
- [Start Here](#start-here)
- [Understanding Each Component](#understanding-each-component)
- [Quick Reference Checklist](#quick-reference-checklist)
- [Complete Worked Examples](#complete-worked-examples)
- [Advanced Techniques](#advanced-techniques)
- [Troubleshooting Common Problems](#troubleshooting-common-problems)
- [Downloads](#downloads)

---

## Start Here {#start-here}

| When to Use C-S-T alone | When to Use full C-S-T + P-I-T |
|--------------------------|----------------------------------|
| The task is straightforward and commonly understood | The output will be used in professional contexts |
| You need a quick response for brainstorming | You need specific formatting, rules + constraints |
| The stakes are relatively low | The task is complex or has multiple requirements |
| | You're working on something mission-critical |

## Understanding Each Component {#understanding-each-component}

### C-S-T Framework

| **C**ontext (The Who) | **S**kill (The How) | **T**ask (The What) |
|----------------------|---------------------|---------------------|
| Sets the AI's expertise level and perspective | Defines communication style and audience awareness | Should be one clear, actionable sentence |
| Influences tone, vocabulary, and approach | Shapes complexity level and explanation depth | Avoid compound requests that could be split |
| Examples: "You are a cybersecurity expert with 15 years of enterprise experience" | Consider technical level, cultural context, time constraints | Be specific about deliverables |

### P-I-T Framework

| **P**urpose (The Why) | **I**ngredients (The Rules) | **T**arget (The Format) |
|----------------------|----------------------------|-------------------------|
| Explains the motivation and end-goal behind your request | Lists the specific constraints, assumptions, and rules | Defines the exact structure and format of the desired output |
| Gives the AI critical context to make better judgment calls | These are the guardrails that narrow the AI's focus | Ensures the result is immediately usable |
| Example: "This is for a formal presentation to the executive board" | Example: "Must be under 280 characters and include one hashtag" | Example: "Provide the output as a JSON array of objects" |

### The Collaborative Process

| **CHECKS** (Quality Check) | **R-E-I-N** (Back on Track) | **Review** (The Finish) |
|---------------------------|----------------------------|-------------------------|
| Ask immediately after your prompt: "What assumptions are you making? List 3 questions for me." | **Restate** - Refocus the lens | **Self-Critique**: "Review your response. What's missing?" |
| Watch the thinking process, not just the outcome | **Edit** - Adjust the rules | **Bias**: "Check for applicable bias; suggest fixes." |
| If you notice the AI heading in the wrong direction mid-response, stop generation and redirect | **Ignore** - Remove distractions | **Summarize & Package**: "Give me a TL;DR then the final answer in [format]" |
| | **Nudge** - Show, don't tell | |

## Quick Reference Checklist {#quick-reference-checklist}

### Before Sending Your Prompt
- [ ] Context clearly defines AI's role and expertise
- [ ] Skill specifies target audience and communication style
- [ ] Task is one clear, actionable sentence
- [ ] Purpose explains the "why" and stakes
- [ ] Ingredients cover all rules + constraints and assumptions
- [ ] Target defines exact output format

### After Initial Response
- [ ] Use CHECKS if response seems off-track
- [ ] Apply R-E-I-N method for course corrections
- [ ] Request self-critique before finalizing
- [ ] Check for biases

### Before Using the Output
- [ ] Verify all 'Ingredients' (rules + constraints) were followed
- [ ] If sources are vital, verify them yourself
- [ ] Check that format matches your needs
- [ ] Ensure tone and style fit your context
- [ ] Confirm technical accuracy (if applicable)

## Complete Worked Examples {#complete-worked-examples}

### Business Content Creation

**❌ Weak Prompt**
*"Write me some social media posts about our new product launch."*

**✅ Strong Prompt Using C-S-T + P-I-T**

**Context:** You are a social media strategist for a B2B SaaS company with expertise in LinkedIn and Twitter marketing.

**Skill:** You write for busy executives and decision-makers who scroll quickly and value concise, actionable insights.

**Task:** Create five social media posts announcing our new project management software launch.

**Purpose:** These posts will run during our launch week to drive sign-ups for our free trial, targeting our goal of 500 new leads.

**Ingredients (Rules + Constraints):**
- Each post must be under 280 characters for Twitter compatibility
- Include exactly one call-to-action per post
- Don't use buzzwords like "revolutionary" or "game-changing"
- Assume our audience struggles with team coordination and missed deadlines
- Focus on concrete benefits, not features

**Target:** Provide output as five numbered posts in a code block, with each post labeled for the intended day (Monday-Friday).

### Technical Documentation

**❌ Weak Prompt**
*"Explain how to set up authentication in our app."*

**✅ Strong Prompt Using C-S-T + P-I-T**

**Context:** You are a senior developer advocate who specializes in creating clear technical documentation for API implementations.

**Skill:** You're writing for mid-level developers who are competent but unfamiliar with our specific authentication system.

**Task:** Write a step-by-step guide for implementing OAuth 2.0 authentication in our customer portal API.

**Purpose:** This will be published in our developer docs to reduce support tickets and improve developer onboarding experience.

**Ingredients:**
- Include code examples in JavaScript and Python
- Assume developers are using standard REST clients
- Don't assume knowledge of our specific token refresh process
- Must include error handling for common failure scenarios
- Keep explanations under 2 sentences per step

**Target:** Format as a numbered tutorial with code blocks, following our docs template: Overview → Prerequisites → Step-by-step → Troubleshooting → Next Steps.

## Advanced Techniques {#advanced-techniques}

Advanced techniques integrate naturally with the framework components rather than replacing them:

| Technique | Description |
|-----------|-------------|
| ⛓️ **Chain of Thought** | Add to your Task section: "Before providing your final answer, walk through your reasoning step-by-step." |
| 👓 **Role Reversal** | After receiving output, ask: "Now critique this from the perspective of [different stakeholder]. What would they object to?" |
| 🌐 **Multi-Perspective Validation** | "Review your response from three perspectives: [Stakeholder A], [Stakeholder B], and [Stakeholder C]. What would each change?" |
| 🪜 **Constraint Laddering** | Start with basic constraints, then add more specific ones in follow-up prompts |
| ✨ **Success Simulation** | Include in Purpose: "Success means [specific person] can [specific action] without needing to ask follow-up questions." |
| 🖇️ **Collaborative Scoping** | For complex projects, use the AI to help you write the prompt by acting as an expert consultant |

## Troubleshooting Common Problems {#troubleshooting-common-problems}

| Problem | Solutions |
|---------|-----------|
| **AI Gives Generic Responses** | Add more specific details to Context; Include concrete rules in Ingredients; Use the "Nudge" technique with examples |
| **AI Misses the Mark Completely** | Use "CHECKS" immediately after initial prompt; Break complex requests into smaller prompts; Add "Success looks like..." to Purpose |
| **Output is Too Long/Short** | Specify exact word/character counts in Constraints; Provide format examples in Target; Use comparison references |
| **AI Ignores Important Rules** | Move critical Ingredients to the beginning; Use R-E-I-N method to course-correct; Repeat key rules in both Ingredients and Target |
| **Response Lacks Creativity** | Add creative Ingredients ("Avoid the obvious approach"); Include inspiration sources in Context; Ask "What would [expert] do differently?" |
| **The "People-Pleaser" Paradox** | Frame differently: "How closely does this match the original requirements?"; Ask for strengths first, then improvements; Give it an 'out' |

## Downloads {#downloads}

### Available Resources

- **[PDF Guide](/downloads/Guide-to-Effective-Prompt-Crafting.pdf)** - Complete guide in PDF format for offline reference
- **[One-Page Framework](/downloads/OnePageCSTPITPromptFramework.pdf)** - Quick reference card with the visual framework
- **[Kettle Bell Challenge Guide](/downloads/Prompt-for-Kettle-Bell-100-for-30-days.pdf)** - Example application of the framework

### Framework Visual Reference

The C-S-T and P-I-T framework can be visualized as building a house:

**C-S-T** = Foundation and frame. Establishes who's doing the work, how they should approach it, and what they're building.

**P-I-T** = Detailed blueprint, electrical plans, and finishing specifications. Defines exactly how work should be executed and what the final result should look like.

---

*This guide complements the Frame Check newsletter's practical approach to AI tools. For more honest ROI analysis and practical AI guidance without the hype, subscribe to Frame Check.*

*Feel free to share this guide - just credit @calmren.com*
