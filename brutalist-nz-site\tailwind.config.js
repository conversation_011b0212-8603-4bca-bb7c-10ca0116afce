/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Brutalist NZ color palette
        'base': '#f5f7fa',
        'text': '#1b1f23',
        'fern': '#0d4225',
        'kawakawa': '#1a5f3f',
        'charcoal': '#111418',
        'mist': '#e8eaed',
      },
      fontFamily: {
        'heading': ['Montserrat', 'sans-serif'],
        'body': ['Inter', 'sans-serif'],
      },
      fontSize: {
        'h1': 'clamp(1.75rem, 4vw, 2.25rem)',
        'h2': '1.75rem',
        'h3': '1.5rem',
      },
      lineHeight: {
        'heading': '1.2',
        'body': '1.6',
      },
      spacing: {
        '1': '0.5rem',
        '2': '1rem',
        '3': '1.5rem',
        '4': '2rem',
        '5': '2.5rem',
        '6': '3rem',
        '7': '3.5rem',
        '8': '4rem',
        '9': '4.5rem',
        '10': '5rem',
        '11': '5.5rem',
        '12': '6rem',
      },
      maxWidth: {
        'site': '1200px',
      },
      gridTemplateColumns: {
        'site': 'repeat(12, 1fr)',
      },
      gap: {
        'gutter': '20px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
