<template>
  <div class="about-page">
    <!-- Hero Section -->
    <section class="about-hero">
      <div class="site-container">
        <div class="hero-content">
          <h1 class="page-title">The Frame Check for AI</h1>
          <p class="hero-subtitle">
            AI advice is everywhere: TikToks shouting "5-minute automations," newsletters promising "life-changing prompts,"
            courses priced like used cars. Sifting signal from click-bait is exhausting.
          </p>
          <p class="hero-lead">
            <strong>Frame Check is the antidote.</strong> I road-test the tools, tally the real return on investment (ROI)
            in time and money, and share the results—no affiliate links, no hype.
          </p>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <section class="about-content">
      <div class="site-container">
        <div class="content-grid">

          <!-- Finding Gold Section -->
          <div class="content-section">
            <h2 class="section-heading">Finding Gold in the Algorithm Rush</h2>
            <p class="section-text">
              Think of me as the prospector who pans so you don't have to. Every week a "revolutionary" app appears.
              Some are worth paying for; most just waste weekends. Frame Check shows which costs make sense and which are fool's gold.
            </p>
          </div>

          <!-- Real Tools Section -->
          <div class="content-section">
            <h2 class="section-heading">Real Tools for Real People</h2>
            <p class="section-text">
              This newsletter is for busy humans—owners, leaders, community builders, up-skillers—who need tools that work,
              not science projects that might work someday. I share what I actually use: which AI excels at code,
              which one nails recipes, which automation blew up at 2 a.m.
            </p>
            <p class="section-text">
              Got a tool you're curious about? Reply any time—your tips guide the next test run.
            </p>
          </div>

          <!-- Learning Together Section -->
          <div class="content-section">
            <h2 class="section-heading">Learning Together (Fuelled by Questionable Coffee Consumption)</h2>
            <p class="section-text">
              I'm a self-confessed tech nerd with a healthy scepticism toward glossy marketing. My coding background lets me peek under the hood;
              my late-night espresso habit keeps the debugging sessions lively. When an automation fails or a subscription turns out to be landfill,
              you'll hear about it, mistakes included.
            </p>
            <p class="section-text">
              Sometimes the tool is fine; we just need better fundamentals. That's why Frame Check pairs reality-checks with skill-guides
              on prompt crafting, workflow design, and more.
            </p>
          </div>

          <!-- Kiwi Values Section -->
          <div class="content-section">
            <h2 class="section-heading">Grounded in Kiwi Values</h2>
            <p class="section-text">
              New Zealand's number-8-wire mentality, DIY ingenuity with whatever's handy, runs through everything here.
              Tikanga (the right way to do things) keeps us focused on substance, not spectacle.
              My kaupapa is simple: protect (kaitiaki) your time, wallet, and trust while we learn together.
            </p>
            <p class="section-text">
              I'm writing for thoughtful people, not algorithms. Expect considered analysis, respectful kōrero, and a dash of dry humour.
            </p>
          </div>

        </div>
      </div>
    </section>

    <!-- What You'll Find Section -->
    <section class="dark-spotlight what-youll-find">
      <div class="site-container">
        <h2 class="section-title">What You'll Find</h2>
        <div class="features-grid">
          <div class="feature-item">
            <h3 class="feature-title">Reality-check trending AI tools & techniques</h3>
          </div>
          <div class="feature-item">
            <h3 class="feature-title">Build lasting capabilities with step-by-step skill guides</h3>
          </div>
          <div class="feature-item">
            <h3 class="feature-title">Share "What I'm Using" workflows that genuinely save hours</h3>
          </div>
          <div class="feature-item">
            <h3 class="feature-title">Explore deeper topics: agentic AI, automation, privacy, AI-assisted coding</h3>
          </div>
          <div class="feature-item">
            <h3 class="feature-title">Connect—send your wins, losses, and questions any time</h3>
          </div>
        </div>
        <div class="closing-statement">
          <p class="closing-text">
            Because the best AI tool is the one that makes life easier—without hype, headaches, or devouring your weekend.
          </p>
        </div>
      </div>
    </section>

    <!-- Glossary Section -->
    <section class="glossary-section">
      <div class="site-container">
        <h2 class="section-title">Kupu (Glossary)</h2>
        <div class="glossary-grid">
          <div class="glossary-item">
            <h3 class="glossary-term">Kōrero</h3>
            <p class="glossary-definition">Story, conversation—our ongoing dialogue</p>
          </div>
          <div class="glossary-item">
            <h3 class="glossary-term">Kaitiakitanga</h3>
            <p class="glossary-definition">Guardianship—protecting your time, money, and trust</p>
          </div>
          <div class="glossary-item">
            <h3 class="glossary-term">Manaakitanga</h3>
            <p class="glossary-definition">Care & kindness—sharing honest experiences, helping each other</p>
          </div>
          <div class="glossary-item">
            <h3 class="glossary-term">Pono</h3>
            <p class="glossary-definition">Truth & integrity—cutting through marketing spin</p>
          </div>
          <div class="glossary-item">
            <h3 class="glossary-term">Kotahitanga</h3>
            <p class="glossary-definition">Unity—AI knowledge should be accessible to everyone</p>
          </div>
          <div class="glossary-item">
            <h3 class="glossary-term">Tikanga</h3>
            <p class="glossary-definition">The right way of doing things—our compass for evaluating AI</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
      <div class="site-container">
        <div class="cta-content">
          <h2 class="cta-title">Frame Check: because stories aren't just told to us—they're framed for us.</h2>
          <p class="cta-subtitle">Let's pay attention.</p>
          <div class="cta-actions">
            <RouterLink to="/articles" class="cta-button primary">
              Read Frame Check
            </RouterLink>
            <RouterLink to="/contact" class="cta-button secondary">
              Get in Touch
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.about-page {
  min-height: 100vh;
}

/* Hero Section */
.about-hero {
  padding: var(--space-12) 0 var(--space-8);
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.8) 100%);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.page-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text);
  margin-bottom: var(--space-6);
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  opacity: 0.9;
}

.hero-lead {
  font-size: 1.375rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-6);
}

.hero-lead strong {
  color: var(--color-kawakawa);
  font-weight: 600;
}

/* Main Content */
.about-content {
  padding: var(--space-10) 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  max-width: 800px;
  margin: 0 auto;
}

.content-section {
  padding: var(--space-6);
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.content-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-heading {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  line-height: 1.3;
}

.section-text {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.section-text:last-child {
  margin-bottom: 0;
}

/* What You'll Find Section */
.what-youll-find {
  padding: var(--space-10) 0;
}

.section-title {
  font-size: 2rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-6);
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.feature-item {
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: white;
  margin: 0;
  line-height: 1.5;
}

.closing-statement {
  text-align: center;
  padding-top: var(--space-6);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.closing-text {
  font-size: 1.25rem;
  font-style: italic;
  color: white;
  opacity: 0.9;
  margin: 0;
}

/* Glossary Section */
.glossary-section {
  padding: var(--space-10) 0;
  background: var(--color-base);
}

.glossary-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 900px;
  margin: 0 auto;
}

.glossary-item {
  padding: var(--space-5);
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.glossary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.glossary-term {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-kawakawa);
  margin-bottom: var(--space-2);
}

.glossary-definition {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--color-text);
  margin: 0;
}

/* Final CTA Section */
.final-cta {
  padding: var(--space-10) 0;
  background: linear-gradient(135deg, var(--color-fern) 0%, var(--color-kawakawa) 100%);
}

.cta-content {
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
}

.cta-title {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-4);
  line-height: 1.3;
}

.cta-subtitle {
  font-size: 1.25rem;
  font-style: italic;
  color: white;
  opacity: 0.9;
  margin-bottom: var(--space-6);
}

.cta-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: var(--space-3) var(--space-6);
  text-decoration: none;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.cta-button.primary {
  background-color: white;
  color: var(--color-kawakawa);
  border-color: white;
}

.cta-button.primary:hover {
  background-color: transparent;
  color: white;
  border-color: white;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background-color: transparent;
  color: white;
  border-color: white;
}

.cta-button.secondary:hover {
  background-color: white;
  color: var(--color-kawakawa);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .glossary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .glossary-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .content-section:hover,
  .glossary-item:hover {
    transform: none;
  }

  .cta-button:hover {
    transform: none;
  }
}
</style>
