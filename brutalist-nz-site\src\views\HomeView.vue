<script setup lang="ts">
// Basic home page for testing layout
</script>

<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="site-container">
        <div class="hero-content">
          <h1 class="hero-title">
            Grounded insight
            <span class="hero-subtitle">Global tech. Kiwi heart.</span>
          </h1>
          <p class="hero-description">
            Long-form thinking and real-world AI advice, exploring how innovation and tikanga shape each other
          </p>
          <div class="hero-actions">
            <RouterLink to="/articles" class="cta-button primary">
              Read Articles
            </RouterLink>
            <RouterLink to="/ai-help" class="cta-button secondary">
              AI Help
            </RouterLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Latest Articles Preview -->
    <section class="latest-articles">
      <div class="site-container">
        <h2 class="section-title">Latest from Substack</h2>
        <div class="articles-grid">
          <!-- Placeholder for article cards -->
          <div class="article-card-placeholder">
            <div class="placeholder-content">
              <div class="placeholder-title"></div>
              <div class="placeholder-excerpt"></div>
              <div class="placeholder-meta"></div>
            </div>
          </div>
          <div class="article-card-placeholder">
            <div class="placeholder-content">
              <div class="placeholder-title"></div>
              <div class="placeholder-excerpt"></div>
              <div class="placeholder-meta"></div>
            </div>
          </div>
          <div class="article-card-placeholder">
            <div class="placeholder-content">
              <div class="placeholder-title"></div>
              <div class="placeholder-excerpt"></div>
              <div class="placeholder-meta"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Dark Spotlight Section -->
    <section class="dark-spotlight about-section">
      <div class="site-container">
        <div class="about-content">
          <h2 class="section-title">The Frame Check for AI</h2>
          <div class="about-intro">
            <p class="about-text lead">
              AI advice is everywhere: TikToks shouting "5-minute automations," newsletters promising "life-changing prompts,"
              courses priced like used cars. Sifting signal from click-bait is exhausting.
            </p>
            <p class="about-text">
              <strong>Frame Check is the antidote.</strong> I road-test the tools, tally the real return on investment (ROI)
              in time and money, and share the results—no affiliate links, no hype.
            </p>
          </div>

          <div class="about-pillars">
            <div class="pillar">
              <h3 class="pillar-title">Finding Gold in the Algorithm Rush</h3>
              <p class="pillar-text">
                Think of me as the prospector who pans so you don't have to. Every week a "revolutionary" app appears.
                Some are worth paying for; most just waste weekends. Frame Check shows which costs make sense and which are fool's gold.
              </p>
            </div>

            <div class="pillar">
              <h3 class="pillar-title">Real Tools for Real People</h3>
              <p class="pillar-text">
                This newsletter is for busy humans—owners, leaders, community builders, up-skillers—who need tools that work,
                not science projects that might work someday. I share what I actually use: which AI excels at code,
                which one nails recipes, which automation blew up at 2 a.m.
              </p>
            </div>

            <div class="pillar">
              <h3 class="pillar-title">Grounded in Kiwi Values</h3>
              <p class="pillar-text">
                New Zealand's number-8-wire mentality, DIY ingenuity with whatever's handy, runs through everything here.
                Tikanga (the right way to do things) keeps us focused on substance, not spectacle.
                My kaupapa is simple: protect (kaitiaki) your time, wallet, and trust while we learn together.
              </p>
            </div>
          </div>

          <div class="about-cta">
            <p class="cta-text">
              Because the best AI tool is the one that makes life easier—without hype, headaches, or devouring your weekend.
            </p>
            <RouterLink to="/articles" class="cta-button primary">
              Read Frame Check
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* Hero Section */
.hero-section {
  padding: var(--space-12) 0 var(--space-8);
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.8) 100%);
}

.hero-content {
  max-width: 800px;
  text-align: center;
  margin: 0 auto;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.hero-subtitle {
  display: block;
  font-size: 0.7em;
  color: var(--color-kawakawa);
  font-style: italic;
  margin-top: var(--space-2);
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-6);
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-block;
  padding: var(--space-3) var(--space-6);
  text-decoration: none;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.cta-button.primary {
  background-color: var(--color-fern);
  color: white;
  border-color: var(--color-fern);
}

.cta-button.primary:hover {
  background-color: var(--color-kawakawa);
  border-color: var(--color-kawakawa);
  transform: translateY(-2px);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--color-fern);
  border-color: var(--color-fern);
}

.cta-button.secondary:hover {
  background-color: var(--color-fern);
  color: white;
  transform: translateY(-2px);
}

/* Latest Articles Section */
.latest-articles {
  padding: var(--space-8) 0;
}

.section-title {
  font-size: 2rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-6);
  text-align: center;
}

.articles-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  max-width: 1000px;
  margin: 0 auto;
}

/* Article Card Placeholders */
.article-card-placeholder {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 8px;
  padding: var(--space-6);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.article-card-placeholder:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.placeholder-title {
  height: 24px;
  background: linear-gradient(90deg, var(--color-mist) 0%, rgba(232, 234, 237, 0.5) 50%, var(--color-mist) 100%);
  border-radius: 4px;
  animation: shimmer 2s infinite;
}

.placeholder-excerpt {
  height: 60px;
  background: linear-gradient(90deg, var(--color-mist) 0%, rgba(232, 234, 237, 0.5) 50%, var(--color-mist) 100%);
  border-radius: 4px;
  animation: shimmer 2s infinite 0.2s;
}

.placeholder-meta {
  height: 16px;
  width: 60%;
  background: linear-gradient(90deg, var(--color-mist) 0%, rgba(232, 234, 237, 0.5) 50%, var(--color-mist) 100%);
  border-radius: 4px;
  animation: shimmer 2s infinite 0.4s;
}

@keyframes shimmer {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Dark Spotlight Section */
.about-section {
  padding: var(--space-10) 0;
}

.about-content {
  max-width: 900px;
  margin: 0 auto;
}

.about-intro {
  text-align: center;
  margin-bottom: var(--space-8);
}

.about-text {
  font-size: 1.125rem;
  line-height: 1.7;
  opacity: 0.9;
  margin-bottom: var(--space-4);
}

.about-text.lead {
  font-size: 1.25rem;
  font-style: italic;
  opacity: 0.95;
}

.about-text strong {
  color: var(--color-kawakawa);
  font-weight: 600;
}

.about-pillars {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.pillar {
  text-align: left;
}

.pillar-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-3);
}

.pillar-text {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.85;
  margin: 0;
}

.about-cta {
  text-align: center;
  padding-top: var(--space-6);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-text {
  font-size: 1.125rem;
  font-style: italic;
  margin-bottom: var(--space-4);
  opacity: 0.9;
}

/* Responsive Design */
@media (min-width: 768px) {
  .articles-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .articles-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .about-pillars {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .cta-button:hover {
    transform: none;
  }

  .article-card-placeholder:hover {
    transform: none;
  }

  .placeholder-title,
  .placeholder-excerpt,
  .placeholder-meta {
    animation: none;
  }
}
</style>
