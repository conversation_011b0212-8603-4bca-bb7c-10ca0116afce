import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/articles',
      name: 'articles',
      component: () => import('../views/ArticlesView.vue'),
    },
    {
      path: '/article/:slug',
      name: 'article',
      component: () => import('../views/ArticleView.vue'),
    },
    {
      path: '/shortform',
      name: 'shortform',
      component: () => import('../views/ShortformView.vue'),
    },
    {
      path: '/ai-help',
      name: 'ai-help',
      component: () => import('../views/AiHelpView.vue'),
    },
    {
      path: '/ai-help/:slug',
      name: 'ai-guide',
      component: () => import('../views/AiGuideView.vue'),
    },
    {
      path: '/workshops',
      name: 'workshops',
      component: () => import('../views/WorkshopsView.vue'),
    },
    {
      path: '/workshops/:id',
      name: 'workshop',
      component: () => import('../views/WorkshopView.vue'),
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/contact',
      name: 'contact',
      component: () => import('../views/ContactView.vue'),
    },
  ],
})

export default router
