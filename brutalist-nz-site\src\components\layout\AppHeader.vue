<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { ref, onMounted } from 'vue'

// Simple mobile menu toggle
const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

// Close mobile menu when clicking outside or on a link
const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}
</script>

<template>
  <header class="site-header">
    <div class="site-container">
      <div class="header-content">
        <!-- Logo/Brand -->
        <div class="brand">
          <RouterLink to="/" class="brand-link" @click="closeMobileMenu">
            <span class="brand-text">Port Calm</span>
            <span class="brand-subtitle">Aotearoa Grounded</span>
          </RouterLink>
        </div>

        <!-- Desktop Navigation -->
        <nav class="desktop-nav" aria-label="Main navigation">
          <RouterLink to="/" class="nav-link">Home</RouterLink>
          <RouterLink to="/articles" class="nav-link">Articles</RouterLink>
          <RouterLink to="/shortform" class="nav-link">Shortform</RouterLink>
          <RouterLink to="/ai-help" class="nav-link">AI Help</RouterLink>
          <RouterLink to="/workshops" class="nav-link">Workshops</RouterLink>
          <RouterLink to="/about" class="nav-link">About</RouterLink>
          <RouterLink to="/contact" class="nav-link">Contact</RouterLink>
        </nav>

        <!-- Mobile Menu Button -->
        <button 
          class="mobile-menu-button"
          @click="toggleMobileMenu"
          :aria-expanded="isMobileMenuOpen"
          aria-controls="mobile-menu"
          aria-label="Toggle navigation menu"
        >
          <span class="hamburger-line" :class="{ 'open': isMobileMenuOpen }"></span>
          <span class="hamburger-line" :class="{ 'open': isMobileMenuOpen }"></span>
          <span class="hamburger-line" :class="{ 'open': isMobileMenuOpen }"></span>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <nav
        class="mobile-nav"
        :class="{ 'open': isMobileMenuOpen }"
        id="mobile-menu"
        aria-label="Mobile navigation"
      >
        <RouterLink to="/" class="mobile-nav-link" @click="closeMobileMenu">Home</RouterLink>
        <RouterLink to="/articles" class="mobile-nav-link" @click="closeMobileMenu">Articles</RouterLink>
        <RouterLink to="/shortform" class="mobile-nav-link" @click="closeMobileMenu">Shortform</RouterLink>
        <RouterLink to="/ai-help" class="mobile-nav-link" @click="closeMobileMenu">AI Help</RouterLink>
        <RouterLink to="/workshops" class="mobile-nav-link" @click="closeMobileMenu">Workshops</RouterLink>
        <RouterLink to="/about" class="mobile-nav-link" @click="closeMobileMenu">About</RouterLink>
        <RouterLink to="/contact" class="mobile-nav-link" @click="closeMobileMenu">Contact</RouterLink>
      </nav>
    </div>
  </header>
</template>

<style scoped>
.site-header {
  background-color: var(--color-base);
  border-bottom: 1px solid var(--color-mist);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
}

/* Brand */
.brand-link {
  text-decoration: none;
  color: var(--color-text);
  transition: color 0.2s ease;
}

.brand-link:hover {
  color: var(--color-fern);
}

.brand-text {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 1.5rem;
  display: block;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 0.75rem;
  color: var(--color-kawakawa);
  font-style: italic;
  display: block;
  margin-top: -2px;
}

/* Desktop Navigation */
.desktop-nav {
  display: none;
  gap: var(--space-6);
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: var(--color-text);
  font-weight: 400;
  transition: color 0.2s ease, transform 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--color-fern);
  transform: translateY(-1px);
}

.nav-link.router-link-active {
  color: var(--color-kawakawa);
  font-weight: 500;
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-kawakawa);
}

/* Mobile Menu Button */
.mobile-menu-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  gap: 4px;
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background-color: var(--color-text);
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-4) 0;
  border-top: 1px solid var(--color-mist);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.mobile-nav.open {
  max-height: 400px;
}

.mobile-nav-link {
  text-decoration: none;
  color: var(--color-text);
  font-weight: 400;
  padding: var(--space-2) 0;
  transition: color 0.2s ease, padding-left 0.2s ease;
}

.mobile-nav-link:hover {
  color: var(--color-fern);
  padding-left: var(--space-2);
}

.mobile-nav-link.router-link-active {
  color: var(--color-kawakawa);
  font-weight: 500;
  padding-left: var(--space-2);
}

/* Desktop styles */
@media (min-width: 768px) {
  .desktop-nav {
    display: flex;
  }
  
  .mobile-menu-button {
    display: none;
  }
  
  .mobile-nav {
    display: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .nav-link:hover {
    transform: none;
  }
  
  .hamburger-line {
    transition: none;
  }
  
  .mobile-nav {
    transition: none;
  }
}
</style>
