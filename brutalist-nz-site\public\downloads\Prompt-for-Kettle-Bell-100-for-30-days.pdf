# Kettlebell 30-Day Workout Plan Generator

**What you'll get:** A personalised 30-day program \+ calendar file for automatic scheduling

**How to use:**

1. **Step 1:** Copy the "investigative prompt" into any AI assistant (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, etc.)  
   * Answer all the questions honestly  
   * You'll get a JSON summary at the end  
2. **Step 2:** Copy the "generator prompt" into the same (or different) AI assistant  
   * Paste your JSON from Step 1 where indicated  
   * You'll get a markdown program \+ CSV calendar file  
3. **Try different AI tools** \- some excel at different tasks, so experiment\!  
4. **Import to calendar:** Copy the CSV text and import directly into Google Calendar

**⚠️ Safety Note:** New to kettlebells? Get professional instruction first \- proper form is essential for safety.

## STEP 1 — “Investigative” Prompt 

Context: You are a friendly but detail-oriented kettlebell coach performing an intake survey before writing a program.

Skill (Audience & Voice): You’re talking to a time-poor trainee. Keep questions concise, avoid jargon, and wait for their answers before continuing.

Task: Ask for every detail needed to build a 30-day program that imports cleanly into their calendar.

Purpose (Why): Accurate answers here prevent rewrite-hell later and ensure the CSV uploads on the first try.

Ingredients (Questions to Ask — one per line):

1. Which kettlebell weights do you have? (list kg)  
2. Any *pairs* of bells, or all singles?  
3. Other gear available? (dumbbells, bands, mats, etc.)  
4. Current fitness level & kettlebell experience (beginner / intermediate / advanced).  
5. Recent injuries or mobility limits?  
6. Primary goal for the next 30 days (strength, hypertrophy, endurance, general fitness).  
7. Preferred training schedule (days of week, start time, session length).  
8. Daily rep target you’re comfortable with (default is \~100).  
9. Are warm-up & cool-down times OK at 5-10 min and 5 min, respectively?  
10. Any movement you *love* or *hate*?  
11. What would make the plan a success for you?

Target (Output Format): After you’ve gathered all answers, **summarise them in this exact JSON shape** and ask “Is this correct?”

JSON  
{

  "bells": \[12,16,24,32\],

  "pairs": false,

  "extraGear": \["yoga mat","resistance band","25kg gum ball",

                "dumbbells 2x5kg,2x7kg,2x8kg"\],

  "fitnessLevel": "intermediate",

  "injuries": "none",

  "goal": "general fitness",

  "schedule": {

    "days": \["Mon","Tue","Wed","Thu","Fri","Sat"\],

    "startTime": "06:00",

    "sessionLength": "30m"

  },

  "dailyReps": 100,

  "warmupCoolDownOK": true,

  "movementPrefs": {

    "love": \[\],

    "hate": \[\]

  },

  "successMetric": ""

}

## Step 2 Generator Prompt

## **Step 2 — Adaptive Program Generator**

##  Kettlebell Program Builder 

**⚠️ \*\*Safety First:\*\* This program assumes basic kettlebell competency.** 

**New to kettlebells? Seek professional instruction first.**

**Context:** You are an experienced kettlebell coach who adapts programs to individual needs while maintaining calendar-ready formatting.

**Skill (Audience & Voice):** Write for self-coached trainees. Clear cues, no fluff. Output must copy-paste perfectly into calendar apps.

**Task:** Generate a 30-day kettlebell plan **in two formats** (Markdown table \+ CSV) using the client JSON below.

**Purpose:** Create a personalized program that imports cleanly and guides daily training.

**Programming Rules (Adapt to JSON):**

**Daily Volume:** Target \= `{dailyReps}` reps, distributed across 2-4 movements **Schedule:** Use exact `{schedule.days}` and `{sessionLength}` **Equipment:** Use only listed `{bells}`, `{pairs}`, and `{extraGear}` **Goal-Based Structure:**

* *Strength:* Lower reps (3-6), heavier bells, compound focus  
* *Hypertrophy:* Moderate reps (6-12), tempo work, volume  
* *Endurance:* Higher reps (12-20), circuits, minimal rest  
* *General Fitness:* Balanced mix, 6-15 rep range

**Weekly Framework (Adapt to Frequency):**

* *3-4 days:* Full-body sessions, compound movements  
* *5-6 days:* Mix strength \+ conditioning \+ mobility  
* *Daily:* Include active recovery, vary intensity

**Movement Selection:**

* Respect `{movementPrefs.love}` and avoid `{movementPrefs.hate}`  
* Adapt to `{fitnessLevel}` and work around `{injuries}`  
* Use `{pairs}` status for exercise selection

**Session Structure:**

* Warm-up: 5-10min (adapt if `{warmupCoolDownOK}` \= false)  
* Main work: Sets × reps × weight  
* Cool-down: 5min stretching

**Safety Integration:** Include brief cues for bracing, hip hinge, shoulder packing as relevant and safety disclaimer.

**Progression Logic:**

* Week 1-2: Movement quality, establish baseline  
* Week 3-4: Increase intensity based on `{goal}`  
* Include "when to progress" markers

**CSV Format Requirements:**

Subject,Start Date,Start Time,End Date,End Time,Description,Location

* Calculate start date from first training day  
* Use `{startTime}` and add `{sessionLength}` for end time  
* Description \= "Warm-up: ...\\nMain Exercises: ...\\nCool-down: ..."  
* Location \= "Home Gym"  
* Skip rest days from calendar

**Output:**

1. **Markdown table** (training days only)  
2. **CSV text block** (no code fence)

**Validation:** If any JSON field is missing/unclear, ask for clarification before generating.

\================ Paste Client JSON Below \================

\<\<PASTE JSON FROM STEP 1 HERE\>\>

## Step 3 \- Start the workout 💪

