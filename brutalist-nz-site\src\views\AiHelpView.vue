<template>
  <div class="ai-help-page">
    <div class="site-container">
      <header class="page-header">
        <h1>AI Help & Guides</h1>
        <p class="page-subtitle">
          Practical AI guidance without the hype. Learn to use AI tools effectively with honest ROI analysis and real-world applications.
        </p>
      </header>

      <section class="guides-section">
        <h2>Available Guides</h2>
        <div class="guides-grid">
          <article v-for="guide in guides" :key="guide.slug" class="guide-card">
            <div class="guide-content">
              <h3>
                <router-link :to="`/ai-help/${guide.slug}`" class="guide-link">
                  {{ guide.title }}
                </router-link>
              </h3>
              <p class="guide-description">
                {{ guide.description }}
              </p>
              <div class="guide-meta">
                <span class="guide-type">{{ guide.type }}</span>
                <span class="guide-length">{{ guide.readTime }}</span>
              </div>
              <div class="guide-downloads" v-if="guide.downloads?.length">
                <strong>Downloads:</strong>
                <a
                  v-for="download in guide.downloads"
                  :key="download.url"
                  :href="download.url"
                  target="_blank"
                  class="download-link"
                >
                  {{ download.icon }} {{ download.title }}
                </a>
              </div>
            </div>
          </article>
        </div>
      </section>

      <section class="about-section">
        <h2>About These Guides</h2>
        <p>
          These guides are part of the <strong>Frame Check</strong> approach to AI tools - focusing on practical applications,
          honest ROI analysis, and real-world results. No hype, just what works.
        </p>
        <p>
          Each guide incorporates New Zealand values of straightforward communication and practical wisdom,
          helping busy professionals make informed decisions about AI tool adoption.
        </p>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGuides } from '@/composables/useGuides'

const { getGuides } = useGuides()
const guides = getGuides()
</script>

<style scoped>
.ai-help-page {
  padding: var(--space-8) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.page-subtitle {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: var(--space-4) auto 0;
  line-height: 1.6;
}

.guides-section {
  margin-bottom: var(--space-12);
}

.guides-section h2 {
  margin-bottom: var(--space-6);
  font-size: var(--text-2xl);
}

.guides-grid {
  display: grid;
  gap: var(--space-6);
}

.guide-card {
  border: 2px solid var(--color-border);
  padding: var(--space-6);
  background: var(--color-background);
}

.guide-card:hover {
  border-color: var(--color-primary);
}

.guide-content h3 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-xl);
}

.guide-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: bold;
}

.guide-link:hover {
  text-decoration: underline;
}

.guide-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.guide-meta {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  font-size: var(--text-sm);
}

.guide-type {
  background: var(--color-primary);
  color: white;
  padding: var(--space-1) var(--space-2);
  font-weight: bold;
  text-transform: uppercase;
  font-size: var(--text-xs);
}

.guide-length {
  color: var(--color-text-secondary);
}

.guide-downloads {
  font-size: var(--text-sm);
}

.guide-downloads strong {
  display: block;
  margin-bottom: var(--space-2);
}

.download-link {
  display: inline-block;
  margin-right: var(--space-4);
  color: var(--color-primary);
  text-decoration: none;
  padding: var(--space-1) 0;
}

.download-link:hover {
  text-decoration: underline;
}

.about-section {
  background: var(--color-background-secondary, #f8f9fa);
  padding: var(--space-6);
  border-left: 4px solid var(--color-primary);
}

.about-section h2 {
  margin-top: 0;
  margin-bottom: var(--space-4);
}

.about-section p {
  line-height: 1.6;
  margin-bottom: var(--space-3);
}

.about-section p:last-child {
  margin-bottom: 0;
}
</style>
