# Prompting Guides

This directory contains the source materials for the AI prompting guides featured on the Port Calm website.

## Files

- `Guide to Effective Prompt Crafting.md` - Complete markdown guide with the C-S-T and P-I-T framework
- `Guide to Effective Prompt Crafting.pdf` - PDF version for download
- `OnePageCSTPITPromptFramework.pdf` - Quick reference card with visual framework
- `Prompt for Kettle Bell 100 for 30 days.md` - Example application of the framework

## Website Integration

The guides are integrated into the website at `/ai-help` with the following features:

### Guide Listing Page (`/ai-help`)
- Overview of available guides
- Download links for PDF resources
- Guide metadata (type, reading time)
- About section explaining the Frame Check approach

### Individual Guide Pages (`/ai-help/{slug}`)
- Full markdown content rendered as HTML
- Navigation back to guide listing
- Download section with PDF resources
- Responsive design with proper typography

### Technical Implementation

The guides are managed through:
- `src/composables/useGuides.ts` - Guide data and content management
- `src/views/AiHelpView.vue` - Guide listing page
- `src/views/AiGuideView.vue` - Individual guide display
- `public/downloads/` - PDF files for download

### Adding New Guides

To add a new guide:

1. Add the guide data to `src/composables/useGuides.ts`
2. Include the full markdown content in the `content` field
3. Add any PDF resources to `public/downloads/`
4. Update the downloads array with the new resource links

### Framework Overview

The C-S-T and P-I-T framework provides a structured approach to AI prompting:

**C-S-T (Foundation)**
- **Context**: Who is the AI (role, expertise)
- **Skill**: How should they communicate (audience, style)
- **Task**: What specific action to take

**P-I-T (Specification)**
- **Purpose**: Why this matters (stakes, goals)
- **Ingredients**: Rules and constraints
- **Target**: Exact output format

**Collaborative Process**
- **CHECKS**: Quality assurance
- **R-E-I-N**: Course correction
- **Review**: Final validation

This approach ensures consistent, high-quality results from AI interactions while maintaining the practical, no-hype philosophy of Frame Check.
