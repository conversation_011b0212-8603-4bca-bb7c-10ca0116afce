<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Auckland time display
const currentTime = ref('')
const currentDate = ref('')

const updateTime = () => {
  const now = new Date()
  const aucklandTime = new Intl.DateTimeFormat('en-NZ', {
    timeZone: 'Pacific/Auckland',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).format(now)
  
  const aucklandDate = new Intl.DateTimeFormat('en-NZ', {
    timeZone: 'Pacific/Auckland',
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(now)
  
  currentTime.value = aucklandTime
  currentDate.value = aucklandDate
}

onMounted(() => {
  updateTime()
  // Update every second
  setInterval(updateTime, 1000)
})
</script>

<template>
  <footer class="site-footer">
    <div class="site-container">
      <div class="footer-content">
        <!-- Main footer content -->
        <div class="footer-main">
          <div class="footer-section">
            <h3 class="footer-heading">Port Calm</h3>
            <p class="footer-description">
              Simple-nuanced insights grounded in Aotearoa. 
              Long-form writing, AI guidance, and thoughtful content.
            </p>
          </div>
          
          <div class="footer-section">
            <h4 class="footer-subheading">Quick Links</h4>
            <nav class="footer-nav">
              <RouterLink to="/articles" class="footer-link">Articles</RouterLink>
              <RouterLink to="/ai-help" class="footer-link">AI Help</RouterLink>
              <RouterLink to="/workshops" class="footer-link">Workshops</RouterLink>
              <RouterLink to="/about" class="footer-link">About</RouterLink>
              <RouterLink to="/contact" class="footer-link">Contact</RouterLink>
            </nav>
          </div>
          
          <div class="footer-section">
            <h4 class="footer-subheading">Connect</h4>
            <div class="social-links">
              <a href="https://portcalm.substack.com/" target="_blank" rel="noopener noreferrer" class="social-link">
                Substack
              </a>
              <!-- Add more social links as needed -->
            </div>
          </div>
          
          <div class="footer-section">
            <h4 class="footer-subheading">Tāmaki Makaurau Time</h4>
            <div class="time-display">
              <div class="current-time">{{ currentTime }}</div>
              <div class="current-date">{{ currentDate }}</div>
            </div>
          </div>
        </div>
        
        <!-- Footer bottom -->
        <div class="footer-bottom">
          <div class="footer-bottom-content">
            <p class="copyright">
              © {{ new Date().getFullYear() }} Port Calm. Made with care in Aotearoa New Zealand.
            </p>
            <div class="footer-meta">
              <span class="kia-ora">Kia ora & Ngā mihi</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.site-footer {
  background-color: var(--color-charcoal);
  color: white;
  margin-top: auto;
}

.footer-content {
  padding: var(--space-8) 0 var(--space-4);
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.footer-heading {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 1.5rem;
  color: white;
  margin: 0;
}

.footer-subheading {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  color: white;
  margin: 0;
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.2s ease, padding-left 0.2s ease;
}

.footer-link:hover {
  color: white;
  padding-left: var(--space-1);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.social-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.2s ease;
}

.social-link:hover {
  color: white;
}

.time-display {
  font-family: 'Inter', monospace;
}

.current-time {
  font-size: 1.25rem;
  font-weight: 500;
  color: white;
}

.current-date {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: var(--space-1);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--space-4);
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  align-items: center;
  text-align: center;
}

.copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  margin: 0;
}

.footer-meta {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.kia-ora {
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
  font-size: 0.875rem;
}

/* Tablet and desktop styles */
@media (min-width: 768px) {
  .footer-main {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
  
  .footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .footer-link:hover {
    padding-left: 0;
  }
}
</style>
